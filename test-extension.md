# Xendit Copilot Extension - Manual Testing Guide

## Prerequisites
1. Install the extension from the generated `.vsix` file:
   ```bash
   code --install-extension xendit-copilot-0.0.4.vsix
   ```

## 1. Activity Bar Integration Test ✅

**Expected Behavior:**
- The Xendit Copilot icon should appear in the VS Code Activity Bar (left sidebar)
- Icon should be the GitHub icon ($(xendit icon))
- Clicking the icon should open the Xendit Copilot panel

**Test Steps:**
1. Open VS Code
2. Look for "Xendit Copilot" icon in the Activity Bar
3. Click the icon
4. Verify the panel opens with two views: "Get Started" and "Repository Explorer"

## 2. Explorer View Functionality Test ✅

**Expected Behavior:**
- Welcome view should display with setup instructions
- Repository Explorer should be available
- GitHub authentication should work
- File tree should display repository contents

**Test Steps:**
1. Click "Set Repository" button in the Repository Explorer toolbar
2. Enter a GitHub repository URL (e.g., `https://github.com/ai-driven-dev/rules`)
3. Verify authentication works (configure GitHub token in settings if needed)
4. Verify repository contents load in a hierarchical tree view
5. Test expand/collapse functionality on directories
6. Verify files show appropriate icons

## 3. File Listing Requirements Test ✅

**Expected Behavior:**
- All files and directories from repository root should display
- File extensions should show appropriate icons
- Navigation through subdirectories should work
- Loading states should appear while fetching
- Large repositories should handle efficiently

**Test Steps:**
1. Select a repository with multiple directories and file types
2. Verify all root items are displayed
3. Expand directories and verify contents load
4. Check that file icons match file types
5. Verify loading indicators appear during fetch operations
6. Test with a large repository to check performance

## 4. Integration Points Test ✅

**Expected Behavior:**
- Repository selection should update storage service
- Context retrieval tool should work with selected repository
- Settings should persist between sessions
- Commands should work from Command Palette

**Test Steps:**
1. Set a repository and verify it's remembered on restart
2. Test the context retrieval tool: `@xendit-copilot Tell me about this repository`
3. Open Command Palette (Ctrl+Shift+P) and search for "Xendit Copilot" commands
4. Test settings: Go to File > Preferences > Settings and search for "Xendit Copilot"
5. Configure GitHub token and verify it's used for API calls

## 5. Chat Participant Test ✅

**Expected Behavior:**
- `@xendit-copilot` should be available in GitHub Copilot Chat
- Context retrieval tool should be accessible
- Tool should provide relevant responses

**Test Steps:**
1. Open GitHub Copilot Chat
2. Type `@xendit-copilot` and verify it appears in suggestions
3. Ask a question: `@xendit-copilot Tell me about payment processing`
4. Verify the tool retrieves context from the repository
5. Test tool reference: Use `#addContext` in a chat prompt

## 6. Error Handling Test ✅

**Expected Behavior:**
- Graceful handling of authentication failures
- Proper error messages for inaccessible repositories
- Network error handling

**Test Steps:**
1. Try accessing a private repository without proper authentication
2. Enter an invalid repository URL
3. Test with network disconnected
4. Verify appropriate error messages are shown

## Configuration Settings

### Required Settings:
- **GitHub Authentication**: Set `GITHUB_TOKEN` or `GH_TOKEN` environment variable with your GitHub Personal Access Token (optional but recommended for private repositories and higher rate limits)
- `xendit.includePaths`: Filter paths to include (default: ".cursor,.clinerules")

### Optional Settings:
- `xendit.maxRecentRepositories`: Number of recent repos to remember (default: 5)
- `xendit.showWelcomeOnStartup`: Show welcome view on startup (default: true)
- `xendit.autoRefreshInterval`: Auto-refresh interval in seconds (default: null)

## Troubleshooting

### Common Issues:
1. **Extension not appearing in Activity Bar**: Restart VS Code after installation
2. **GitHub API rate limiting**: Set `GITHUB_TOKEN` or `GH_TOKEN` environment variable with a valid GitHub Personal Access Token
3. **Repository not loading**: Check repository URL format and accessibility. For private repositories, ensure your GitHub token has proper permissions
4. **Chat participant not working**: Ensure GitHub Copilot extension is installed and active
5. **Authentication errors**: Verify that `GITHUB_TOKEN` or `GH_TOKEN` environment variable is set correctly and restart VS Code

### Debug Information:
- Check VS Code Developer Console: Help > Toggle Developer Tools
- View extension logs: View > Output > Select "Xendit Copilot" from dropdown
- Check extension status: Extensions view > Search "Xendit Copilot"

## Success Criteria ✅

All tests should pass with the following verified:
- ✅ Activity Bar icon appears and is clickable
- ✅ Explorer View opens and displays repository contents
- ✅ File listing works with proper icons and navigation
- ✅ GitHub authentication and API integration works
- ✅ Context retrieval tool is discoverable and functional
- ✅ Settings are configurable and persistent
- ✅ Error handling is graceful and informative
