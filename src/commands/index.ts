import * as vscode from "vscode";
import type { IGitHubApiService } from "../api/github";
import type { GithubRepository } from "../api/types";
import type { ILogger } from "../services/logger";
import type { IStorageService } from "../services/storage";
import type { ExplorerView } from "../views/explorer/explorerView";
import type { ExplorerTreeItem } from "../views/explorer/treeItem";
import type { WelcomeView } from "../views/welcome/welcomeView";

interface CommandDependencies {
  context: vscode.ExtensionContext;
  explorerView: ExplorerView;
  githubService: IGitHubApiService;
  logger: ILogger;
  storageService: IStorageService;
  getWelcomeView: () => WelcomeView | null;
  // Removed updatesTreeProvider from dependencies
}

export function registerCommands(dependencies: CommandDependencies): void {
  const { context, explorerView, githubService, logger, storageService, getWelcomeView } = dependencies; // Added githubService to destructuring

  context.subscriptions.push(
    vscode.commands.registerCommand(
      "xendit.setRepository",
      (repository?: GithubRepository) => {
        if (repository) {
          explorerView.setRepository(repository).catch((error) => {
            logger.error("Error setting repository directly", error);

            vscode.window.showErrorMessage(
              `Error setting repository: ${error instanceof Error ? error.message : String(error)}`,
            );
          });
        } else {
          explorerView.promptForRepository();
        }
      },
    ), // Close the first push call correctly
  ); // End of the first push call

  // Keep the original setRepository registration.
  // ExplorerView.setRepository will be modified to handle updating UpdatesTreeProvider.

  context.subscriptions.push(
    vscode.commands.registerCommand("xendit.refresh", () => {
      explorerView.refreshView();
      // Keep refresh separate for now. User uses the specific refresh status button for updates.
    }),
  );

  context.subscriptions.push(
    vscode.commands.registerCommand(
      "xendit.toggleSelection",
      (item: ExplorerTreeItem) => {
        explorerView.handleToggleSelectionCommand(item);
      },
    ),
  );

  context.subscriptions.push(
    vscode.commands.registerCommand(
      "xendit.selectFolder",
      (item: ExplorerTreeItem) => {
        explorerView.handleSelectFolderCommand(item);
      },
    ),
  );

  context.subscriptions.push(
    vscode.commands.registerCommand(
      "xendit.selectAllInFolder",
      (item: ExplorerTreeItem) => {
        explorerView.handleSelectAllInFolderCommand(item);
      },
    ),
  );

  context.subscriptions.push(
    vscode.commands.registerCommand(
      "xendit.deselectAllInFolder",
      (item: ExplorerTreeItem) => {
        explorerView.handleDeselectAllInFolderCommand(item);
      },
    ),
  );

  context.subscriptions.push(
    vscode.commands.registerCommand("xendit.downloadSelected", () => {
      explorerView.downloadSelectedFiles();
    }),
  );

  context.subscriptions.push(
    vscode.commands.registerCommand("xendit.previewSelected", () => {
      explorerView.previewSelectedFiles();
    }),
  );

  context.subscriptions.push(
    vscode.commands.registerCommand("xendit.previewFile", (item: ExplorerTreeItem) => {
      explorerView.previewSingleFile(item);
    }),
  );

  context.subscriptions.push(
    vscode.commands.registerCommand("xendit.clearStorage", async () => {
      logger.debug("Clear storage command executed");
      const confirm = await vscode.window.showWarningMessage(
        "This will clear all Xendit Copilot storage, including recent repositories and settings. Are you sure?",
        { modal: true },
        "Yes",
      );
      if (confirm === "Yes") {
        storageService.clearStorage();

        // Reset the explorer view completely
        explorerView.resetView();

        // Refresh the welcome view to clear recent repositories list
        const welcomeView = getWelcomeView();
        if (welcomeView) {
          welcomeView.refreshRecentRepositories();
        }

        vscode.window.showInformationMessage(
          "Xendit Copilot storage has been cleared.",
        );
      }
    }),
  );

  context.subscriptions.push(
    vscode.commands.registerCommand("xendit.showOutput", () => {
      logger.show();
    }),
  );

  context.subscriptions.push(
    vscode.commands.registerCommand("xendit.openSettings", () => {
      logger.debug("Open settings command executed");
      vscode.commands.executeCommand(
        "workbench.action.openSettings",
        "@ext:xendit-copilot xendit.",
      );
    }),
  );

  // Register command to test GitHub token configuration
  context.subscriptions.push(
    vscode.commands.registerCommand("xendit.testGitHubToken", async () => {
      logger.debug("Test GitHub token command executed");

      // Check environment variables
      const githubToken = process.env.GITHUB_TOKEN;
      const ghToken = process.env.GH_TOKEN;

      let message = "GitHub Token Test Results:\n\n";

      if (githubToken) {
        message += `✅ GITHUB_TOKEN: Found (${githubToken.substring(0, 8)}...)\n`;
      } else {
        message += "❌ GITHUB_TOKEN: Not found\n";
      }

      if (ghToken) {
        message += `✅ GH_TOKEN: Found (${ghToken.substring(0, 8)}...)\n`;
      } else {
        message += "❌ GH_TOKEN: Not found\n";
      }

      if (!githubToken && !ghToken) {
        message += "\n🔧 Setup Instructions:\n";
        message += "1. Set environment variable: export GITHUB_TOKEN=your_token_here\n";
        message += "2. Restart VS Code completely\n";
        message += "3. Or launch VS Code from terminal: code .\n";
        message += "4. Generate token at: https://github.com/settings/tokens";

        vscode.window.showWarningMessage(message, "Open Token Settings").then(selection => {
          if (selection === "Open Token Settings") {
            vscode.env.openExternal(vscode.Uri.parse("https://github.com/settings/tokens"));
          }
        });
      } else {
        // Test API connectivity
        try {
          const testResult = await githubService.fetchRepositoryContent({
            owner: "octocat",
            name: "Hello-World"
          });

          if (testResult.success) {
            message += "\n✅ GitHub API Test: Success! Token is working correctly.";
            vscode.window.showInformationMessage(message);
          } else {
            message += `\n❌ GitHub API Test: Failed - ${testResult.error.message}`;
            vscode.window.showErrorMessage(message);
          }
        } catch (error) {
          message += `\n❌ GitHub API Test: Error - ${error instanceof Error ? error.message : String(error)}`;
          vscode.window.showErrorMessage(message);
        }
      }

      logger.info(`GitHub token test completed - GITHUB_TOKEN: ${!!githubToken}, GH_TOKEN: ${!!ghToken}`);
    }),
  );

  // Register the new command to check for rule updates
  context.subscriptions.push(
    vscode.commands.registerCommand("xendit.refreshRuleStatus", () => {
      logger.debug("Refresh rule status command executed");
      // Only refresh the main explorer view (which now handles status)
      explorerView.treeProvider.refreshAndUpdateStatus().catch((error) => {
        logger.error("Error executing refreshRuleStatus command", error);
        // Error is handled and shown by the method itself
      });
    }),
  );

  // Register command to clear includePaths filter
  context.subscriptions.push(
    vscode.commands.registerCommand("xendit.clearIncludePaths", async () => {
      logger.debug("Clear include paths command executed");

      const config = vscode.workspace.getConfiguration("xendit");
      const currentPaths = config.get<string>("includePaths");

      if (!currentPaths || !currentPaths.trim()) {
        vscode.window.showInformationMessage("Include paths filter is already empty.");
        return;
      }

      const confirm = await vscode.window.showInformationMessage(
        `Clear include paths filter? This will show all repository content.\n\nCurrent filter: ${currentPaths}`,
        "Clear Filter",
        "Cancel"
      );

      if (confirm === "Clear Filter") {
        await config.update("includePaths", "", vscode.ConfigurationTarget.Workspace);
        vscode.window.showInformationMessage("Include paths filter cleared. Repository will refresh automatically.");
        logger.info("Include paths filter cleared by user");
      }
    }),
  );
}
