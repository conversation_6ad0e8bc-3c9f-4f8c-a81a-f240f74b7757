import * as vscode from "vscode";
import type { GithubContent } from "../../api/types";
import type { ILogger } from "../../services/logger";
import { ExplorerTreeItem } from "./treeItem";

export class TreeItemFactory {
  constructor(
    private readonly extensionPath: string,
    private readonly logger: ILogger,
  ) {}

  public createItem(
    content: GithubContent,
    parent?: ExplorerTreeItem,
  ): ExplorerTreeItem {
    return new ExplorerTreeItem(content, parent, this.extensionPath);
  }

  public createLoadingPlaceholder(): ExplorerTreeItem {
    const loadingContent: GithubContent = {
      name: "Chargement...",
      path: "__loading__",
      sha: "",
      size: 0,
      url: "",
      html_url: "",
      git_url: "",
      download_url: null,
      type: "file",
    };
    const item = this.createItem(loadingContent);
    item.collapsibleState = vscode.TreeItemCollapsibleState.None;
    item.description = "Récupération des données...";
    item.iconPath = new vscode.ThemeIcon("loading~spin");
    return item;
  }

  public createErrorPlaceholder(error: unknown): ExplorerTreeItem {
    const errorContent: GithubContent = {
      name: "Erreur de chargement",
      path: "__error__",
      sha: "",
      size: 0,
      url: "",
      html_url: "",
      git_url: "",
      download_url: null,
      type: "file",
    };
    const item = this.createItem(errorContent);
    item.collapsibleState = vscode.TreeItemCollapsibleState.None;
    const errorMessage = error instanceof Error ? error.message : String(error);
    item.description = errorMessage;
    item.iconPath = new vscode.ThemeIcon("error");
    item.tooltip = `Erreur: ${errorMessage}`;
    this.logger.warn(`Created error placeholder: ${errorMessage}`);
    return item;
  }

  public createNoRepositoryPlaceholder(): ExplorerTreeItem {
    const noRepoContent: GithubContent = {
      name: "No repository selected",
      path: "__no_repository__",
      sha: "",
      size: 0,
      url: "",
      html_url: "",
      git_url: "",
      download_url: null,
      type: "file",
    };
    const item = this.createItem(noRepoContent);
    item.collapsibleState = vscode.TreeItemCollapsibleState.None;
    item.description = "Click 'Set Repository' to get started";
    item.iconPath = new vscode.ThemeIcon("repo");
    item.command = {
      command: "xendit.setRepository",
      title: "Set Repository",
    };
    return item;
  }

  public createFilteredContentPlaceholder(filters: string[]): ExplorerTreeItem {
    const filteredContent: GithubContent = {
      name: "No files match current filters",
      path: "__filtered__",
      sha: "",
      size: 0,
      url: "",
      html_url: "",
      git_url: "",
      download_url: null,
      type: "file",
    };
    const item = this.createItem(filteredContent);
    item.collapsibleState = vscode.TreeItemCollapsibleState.None;
    item.description = `Click to clear filter: ${filters.join(", ")}`;
    item.iconPath = new vscode.ThemeIcon("filter");
    item.tooltip = `Repository content is being filtered by includePaths setting.\nActive filters: ${filters.join(", ")}\n\nClick this item to clear the filter and show all repository content.\nOr manually adjust the 'xendit.includePaths' setting.`;
    item.command = {
      command: "xendit.clearIncludePaths",
      title: "Clear Include Paths Filter",
    };
    this.logger.info(`Created filtered content placeholder with filters: [${filters.join(", ")}]`);
    return item;
  }

  public createEmptyRepositoryPlaceholder(): ExplorerTreeItem {
    const emptyContent: GithubContent = {
      name: "Repository appears to be empty",
      path: "__empty__",
      sha: "",
      size: 0,
      url: "",
      html_url: "",
      git_url: "",
      download_url: null,
      type: "file",
    };
    const item = this.createItem(emptyContent);
    item.collapsibleState = vscode.TreeItemCollapsibleState.None;
    item.description = "No files or directories found";
    item.iconPath = new vscode.ThemeIcon("folder");
    item.tooltip = "This repository appears to be empty or all content has been filtered out.";
    return item;
  }
}
